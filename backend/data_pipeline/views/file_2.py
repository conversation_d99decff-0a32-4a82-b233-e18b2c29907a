from django.urls import path
from rest_framework.routers import SimpleRouter
from data_pipeline.views.project import DataPipelineProjectModelViewSet
from data_pipeline.views.file import DataPipelineFileModelViewSet, sse_progress_view_upload
from .views.influxdb import InfluxDBQueryViewSet
from .views.minio import MinioStorageView, sse_progress_view_minio
from .utils.influx_client import sse_progress_view_influxdb

# ================================================= #
# ************** 序列化器 ************** #
# ================================================= #

class DataPipelineFileSerializer(CustomModelSerializer):
    """
    数据管道文件-序列化器
    """
    class Meta:
        model = DataPipelineFile
        fields = "__all__"
        read_only_fields = ["id"]


# ================================================= #
# ************** 视图集 ************** #
# ================================================= #

class DataPipelineFileModelViewSet(CustomModelViewSet):
    """
    数据管道文件管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    upload_file:上传文件
    """
    queryset = DataPipelineFile.objects.all()
    serializer_class = DataPipelineFileSerializer
    permission_classes = [IsAuthenticated]  # 添加认证要求
    # 移除数据权限过滤器，只保留核心模型过滤器
    extra_filter_class = [CoreModelFilterBankend]
    filter_fields = ['project_number', 'file_name', 'file_status', 'version']
    search_fields = ['project_number', 'file_name']

    @action(methods=['POST'], detail=False)
    def upload_file(self, request):
        """
        文件上传处理
        """
        try:
            files = request.FILES.getlist('files')
            is_folder = request.POST.get('isFolder', 'false').lower() == 'true'
            folder_name = request.POST.get('folderName', '')
            project_type = request.POST.get('projectType', '')

            if not files:
                return ErrorResponse(msg='没有接收到文件')

            # 基础上传目录
            base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')
            
            # 如果目录存在，先清空目录
            if os.path.exists(base_upload_dir):
                try:
                    shutil.rmtree(base_upload_dir)
                except Exception as e:
                    print(f"删除文件时出错: {str(e)}")
            
            # 重新创建上传目录
            os.makedirs(base_upload_dir, exist_ok=True)

            # 如果是文件夹上传，创建文件夹目录
            if is_folder and folder_name:
                # 创建以时间戳为前缀的文件夹，避免同名冲突
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_path = f"{folder_name}_{timestamp}" # uploadFiles/dataFolder_20250422_132656
                # folder_path = f"{folder_name}"
                upload_dir = os.path.join(base_upload_dir, folder_path)
                os.makedirs(upload_dir, exist_ok=True)
            else:
                upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')

            uploaded_files = []
            for file in files:
                try:
                    # 构建项目编号：如果是文件夹则使用文件夹名称，否则使用temp
                    project_number = folder_name if is_folder else "temp"
                    
                    # 检查文件是否已存在
                    existing_file = DataPipelineFile.objects.filter(
                        project_number=project_number,
                        file_name=file.name
                    ).first()

                    if existing_file:
                        latest_version = DataPipelineFile.objects.filter(
                            project_number=project_number,
                            file_name=file.name
                        ).order_by('-version').first().version
                        new_version = latest_version + 1
                    else:
                        new_version = 1

                    # 保持原始的文件路径结构（如果是文件夹上传）
                    if is_folder:
                        # 获取文件的相对路径
                        relative_path = file.name
                        if '/' in relative_path:
                            # 创建子文件夹
                            sub_folder = os.path.dirname(relative_path)
                            full_sub_folder = os.path.join(upload_dir, sub_folder)
                            os.makedirs(full_sub_folder, exist_ok=True)
                            
                            # 文件名使用最后一部分
                            file_name = os.path.basename(relative_path)
                        else:
                            file_name = relative_path
                    else:
                        file_name = file.name

                    # 生成安全的文件名
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    safe_filename = f"{file_name}"
                    
                    # 构建完整的文件路径
                    if is_folder and '/' in file.name:
                        sub_folder = os.path.dirname(file.name)
                        file_path = os.path.join(upload_dir, sub_folder, safe_filename)
                    else:
                        file_path = os.path.join(upload_dir, safe_filename)

                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                    # 写入文件
                    with open(file_path, 'wb+') as destination:
                        for chunk in file.chunks():
                            destination.write(chunk)

                    # 创建文件记录
                    file_record = DataPipelineFile.objects.create(
                        project_number=project_number,
                        file_name=file_name,
                        file_path=file_path,
                        file_size=file.size,
                        file_status=1,
                        version=new_version
                    )

                    uploaded_files.append({
                        'id': file_record.id,
                        'name': file_name,
                        'size': file.size,
                        'path': file_path,
                        'version': new_version
                    })

                except Exception as e:
                    # 单个文件处理失败，记录错误但继续处理其他文件
                    print(f"处理文件 {file.name} 时出错: {str(e)}")
                    continue

            if not uploaded_files:
                return ErrorResponse(msg='没有文件上传成功')

            return SuccessResponse(data=uploaded_files, msg='文件上传成功')

        except Exception as e:
            return ErrorResponse(msg=f'文件上传失败：{str(e)}')


async def sse_progress_view_upload(request):
    """
    文件上传进度SSE视图
    """
    async def event_stream():
        try:
            # 获取实际的文件数据
            files = request.FILES.getlist('files')
            is_folder = request.POST.get('isFolder', 'false').lower() == 'true'
            folder_name = request.POST.get('folderName', '')
            project_type = request.POST.get('projectType', '')

            if not files:
                yield f"data: {json.dumps({'error': '没有接收到文件'})}\n\n"
                return

            total_files = len(files)
            current_file = 0
            uploaded_files = []
            
            # 基础上传目录
            base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')
            
            # 如果目录存在，先清空目录
            if os.path.exists(base_upload_dir):
                try:
                    shutil.rmtree(base_upload_dir)
                except Exception as e:
                    print(f"删除文件时出错: {str(e)}")
            
            # 重新创建上传目录
            os.makedirs(base_upload_dir, exist_ok=True)

            # 如果是文件夹上传，创建文件夹目录
            if is_folder and folder_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_path = f"{folder_name}_{timestamp}"
                upload_dir = os.path.join(base_upload_dir, folder_path)
                os.makedirs(upload_dir, exist_ok=True)
            else:
                upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')
                os.makedirs(upload_dir, exist_ok=True)
            
            for file in files:
                try:
                    current_file += 1
                    progress = int((current_file / total_files) * 100)
                    
                    # 发送进度数据
                    progress_data = {
                        'current': current_file,
                        'total': total_files,
                        'progress': progress,
                        'status': 'Processing' if progress < 100 else 'Completed',
                        'current_file': file.name
                    }
                    
                    yield f"data: {json.dumps(progress_data)}\n\n"
                    
                    # 构建项目编号：如果是文件夹则使用文件夹名称，否则使用temp
                    project_number = folder_name if is_folder else "temp"
                    
                    # 检查文件是否已存在
                    existing_file = DataPipelineFile.objects.filter(
                        project_number=project_number,
                        file_name=file.name
                    ).first()

                    if existing_file:
                        latest_version = DataPipelineFile.objects.filter(
                            project_number=project_number,
                            file_name=file.name
                        ).order_by('-version').first().version
                        new_version = latest_version + 1
                    else:
                        new_version = 1

                    # 保持原始的文件路径结构（如果是文件夹上传）
                    if is_folder:
                        # 获取文件的相对路径
                        relative_path = file.name
                        if '/' in relative_path:
                            # 创建子文件夹
                            sub_folder = os.path.dirname(relative_path)
                            full_sub_folder = os.path.join(upload_dir, sub_folder)
                            os.makedirs(full_sub_folder, exist_ok=True)
                            
                            # 文件名使用最后一部分
                            file_name = os.path.basename(relative_path)
                        else:
                            file_name = relative_path
                    else:
                        file_name = file.name

                    # 生成安全的文件名
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    safe_filename = f"{file_name}"
                    
                    # 构建完整的文件路径
                    if is_folder and '/' in file.name:
                        sub_folder = os.path.dirname(file.name)
                        file_path = os.path.join(upload_dir, sub_folder, safe_filename)
                    else:
                        file_path = os.path.join(upload_dir, safe_filename)

                    # 确保目标目录存在
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                    # 写入文件
                    with open(file_path, 'wb+') as destination:
                        for chunk in file.chunks():
                            destination.write(chunk)

                    # 创建文件记录
                    file_record = DataPipelineFile.objects.create(
                        project_number=project_number,
                        file_name=file_name,
                        file_path=file_path,
                        file_size=file.size,
                        file_status=1,
                        version=new_version
                    )

                    uploaded_files.append({
                        'id': file_record.id,
                        'name': file_name,
                        'size': file.size,
                        'path': file_path,
                        'version': new_version
                    })

                except Exception as e:
                    # 单个文件处理失败，记录错误但继续处理其他文件
                    error_data = {
                        'error': f'处理文件 {file.name} 时出错: {str(e)}',
                        'status': 'Error'
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"
                    continue
            
            # 发送完成数据
            completion_data = {
                'current': total_files,
                'total': total_files,
                'progress': 100,
                'status': 'Completed',
                'uploaded_files': uploaded_files
            }
            
            yield f"data: {json.dumps(completion_data)}\n\n"
            
        except Exception as e:
            error_data = {
                'error': str(e),
                'status': 'Error'
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    response = StreamingHttpResponse(
        event_stream(),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    response['X-Accel-Buffering'] = 'no'
    return response